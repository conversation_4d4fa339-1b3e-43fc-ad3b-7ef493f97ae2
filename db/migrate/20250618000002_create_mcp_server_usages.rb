# frozen_string_literal: true

class CreateMcpServerUsages < ActiveRecord::Migration[7.1]
  def change
    create_table :mcp_server_usages do |t|
      t.references :link, null: false, foreign_key: true, index: true
      t.references :purchase, null: false, foreign_key: true, index: true
      t.string :api_key_hash, null: false
      t.integer :api_calls_count, default: 0
      t.integer :total_requests, default: 0
      t.integer :successful_requests, default: 0
      t.integer :failed_requests, default: 0
      t.decimal :total_response_time_ms, precision: 10, scale: 2, default: 0.0
      t.decimal :average_response_time_ms, precision: 8, scale: 2, default: 0.0
      t.json :usage_metadata
      t.date :usage_date, null: false
      t.timestamps

      t.index [:link_id, :usage_date]
      t.index [:purchase_id, :usage_date]
      t.index [:api_key_hash, :usage_date]
      t.index :usage_date
    end
  end
end
