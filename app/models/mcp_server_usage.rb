# frozen_string_literal: true

class McpServerUsage < ApplicationRecord
  belongs_to :link, class_name: "<PERSON>"
  belongs_to :purchase

  validates :api_key_hash, presence: true
  validates :usage_date, presence: true
  validates :api_calls_count, :total_requests, :successful_requests, :failed_requests,
            numericality: { greater_than_or_equal_to: 0 }
  validates :total_response_time_ms, :average_response_time_ms,
            numericality: { greater_than_or_equal_to: 0.0 }

  validate :belongs_to_mcp_server_product

  # Set default values for JSON fields
  after_initialize :set_defaults

  scope :for_date, ->(date) { where(usage_date: date) }
  scope :for_date_range, ->(start_date, end_date) { where(usage_date: start_date..end_date) }
  scope :for_api_key, ->(api_key_hash) { where(api_key_hash: api_key_hash) }
  scope :recent, -> { order(usage_date: :desc) }

  def success_rate
    return 0.0 if total_requests.zero?
    (successful_requests.to_f / total_requests * 100).round(2)
  end

  def failure_rate
    return 0.0 if total_requests.zero?
    (failed_requests.to_f / total_requests * 100).round(2)
  end

  def increment_api_call!(response_time_ms: 0, success: true)
    increment!(:api_calls_count)
    increment!(:total_requests)

    if success
      increment!(:successful_requests)
    else
      increment!(:failed_requests)
    end

    if response_time_ms > 0
      new_total_time = total_response_time_ms + response_time_ms
      new_average = new_total_time / total_requests

      update!(
        total_response_time_ms: new_total_time,
        average_response_time_ms: new_average
      )
    end
  end

  def self.record_usage(link:, purchase:, api_key_hash:, response_time_ms: 0, success: true, date: Date.current)
    usage = find_or_create_by(
      link: link,
      purchase: purchase,
      api_key_hash: api_key_hash,
      usage_date: date
    )

    usage.increment_api_call!(response_time_ms: response_time_ms, success: success)
    usage
  end

  private

  def set_defaults
    self.usage_metadata ||= {}
  end

  def belongs_to_mcp_server_product
    return unless link

    unless link.native_type == Link::NATIVE_TYPE_MCP_SERVER
      errors.add(:link, "must be an MCP server product")
    end
  end
end
