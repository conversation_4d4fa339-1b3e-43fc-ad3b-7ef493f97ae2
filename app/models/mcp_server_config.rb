# frozen_string_literal: true

class McpServerConfig < ApplicationRecord
  HEALTH_STATUSES = %w[healthy unhealthy unknown maintenance].freeze

  belongs_to :link, class_name: "Link"

  validates :endpoint_url, presence: true, format: { with: URI::DEFAULT_PARSER.make_regexp(%w[http https]) }
  validates :health_status, inclusion: { in: HEALTH_STATUSES }
  validates :link, presence: true

  validate :belongs_to_mcp_server_product

  # Set default values for JSON fields
  after_initialize :set_defaults

  scope :active, -> { where(is_active: true) }
  scope :healthy, -> { where(health_status: 'healthy') }
  scope :unhealthy, -> { where(health_status: 'unhealthy') }

  def healthy?
    health_status == 'healthy'
  end

  def unhealthy?
    health_status == 'unhealthy'
  end

  def needs_health_check?
    last_health_check_at.nil? || last_health_check_at < 5.minutes.ago
  end

  def mark_healthy!
    update!(health_status: 'healthy', last_health_check_at: Time.current)
  end

  def mark_unhealthy!
    update!(health_status: 'unhealthy', last_health_check_at: Time.current)
  end

  def mark_maintenance!
    update!(health_status: 'maintenance', last_health_check_at: Time.current)
  end

  private

  def set_defaults
    self.supported_tools ||= []
    self.server_metadata ||= {}
  end

  def belongs_to_mcp_server_product
    return unless link

    unless link.native_type == Link::NATIVE_TYPE_MCP_SERVER
      errors.add(:link, "must be an MCP server product")
    end
  end
end
